<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Projects Fix - Flori Construction</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #005a87; }
        .btn-secondary { background: #6c757d; }
        .btn-secondary:hover { background: #545b62; }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .project-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }
        .project-image {
            height: 200px;
            background: #f0f0f0;
            background-size: cover;
            background-position: center;
        }
        .project-content {
            padding: 15px;
        }
        .project-title {
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        .project-meta {
            color: #666;
            margin: 5px 0;
        }
        .project-description {
            color: #333;
            margin: 10px 0;
        }
        .project-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
        }
        .project-type {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .project-type.completed {
            background: #d4edda;
            color: #155724;
        }
        .project-type.ongoing {
            background: #fff3cd;
            color: #856404;
        }
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .empty-state i {
            color: #ccc;
            margin-bottom: 20px;
        }
        .filters {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        .filters select, .filters input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Projects Fix Test</h1>
        
        <div class="status info" id="status">
            Ready to test projects functionality
        </div>

        <div style="margin: 20px 0;">
            <button class="btn" onclick="testLogin()">🔑 Login</button>
            <button class="btn" onclick="testProjectsManager()">🔍 Test Manager</button>
            <button class="btn" onclick="loadProjectsManually()">📋 Load Projects</button>
            <button class="btn btn-secondary" onclick="clearLog()">🗑️ Clear Log</button>
        </div>

        <!-- Simulate the projects page structure -->
        <div id="projects-page" class="page active">
            <div class="page-header">
                <h2>Projects</h2>
                <button id="add-project-btn" class="btn">
                    <i class="fas fa-plus"></i> Add Project
                </button>
            </div>

            <div class="filters">
                <select id="project-type-filter">
                    <option value="">All Projects</option>
                    <option value="completed">Completed</option>
                    <option value="ongoing">Ongoing</option>
                </select>
                <input type="search" id="project-search" placeholder="Search projects...">
            </div>

            <div id="projects-list" class="projects-grid">
                <!-- Projects will be loaded here -->
            </div>

            <div id="projects-pagination" class="pagination">
                <!-- Pagination will be loaded here -->
            </div>
        </div>

        <div class="log" id="log"></div>
    </div>

    <!-- Load the required scripts -->
    <script src="js/app.js"></script>
    <script src="js/projects.js"></script>

    <script>
        // Override console methods to capture logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToLog(message, type = 'log') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog(args.join(' '), 'warn');
        };

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        async function testLogin() {
            console.log('🔑 Testing login...');
            updateStatus('Testing login...', 'info');
            
            const username = prompt('Enter username:') || 'admin';
            const password = prompt('Enter password:') || 'admin123';
            
            try {
                const response = await fetch('../api/auth.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                console.log('Login response:', data);
                
                if (data.success) {
                    localStorage.setItem('flori_token', data.token);
                    if (window.floriAdmin) {
                        window.floriAdmin.token = data.token;
                        window.floriAdmin.user = data.user;
                    }
                    updateStatus('Login successful!', 'success');
                } else {
                    updateStatus('Login failed: ' + (data.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                console.error('Login error:', error);
                updateStatus('Login error: ' + error.message, 'error');
            }
        }

        function testProjectsManager() {
            console.log('🔍 Testing ProjectsManager...');
            updateStatus('Testing ProjectsManager...', 'info');
            
            if (window.ProjectsManager) {
                console.log('ProjectsManager available:', {
                    isInitialized: window.ProjectsManager.isInitialized,
                    currentPage: window.ProjectsManager.currentPage,
                    projectsCount: window.ProjectsManager.projects.length
                });
                updateStatus('ProjectsManager is available and initialized', 'success');
            } else {
                console.error('ProjectsManager not available');
                updateStatus('ProjectsManager not available', 'error');
            }
            
            if (window.floriAdmin) {
                console.log('floriAdmin available:', {
                    token: window.floriAdmin.token ? 'present' : 'missing',
                    user: window.floriAdmin.user ? 'present' : 'missing'
                });
            } else {
                console.error('floriAdmin not available');
            }
        }

        async function loadProjectsManually() {
            console.log('📋 Loading projects manually...');
            updateStatus('Loading projects...', 'info');
            
            try {
                if (window.loadProjects) {
                    await window.loadProjects();
                    updateStatus('Projects loaded successfully!', 'success');
                } else if (window.ProjectsManager) {
                    await window.ProjectsManager.load();
                    updateStatus('Projects loaded successfully!', 'success');
                } else {
                    throw new Error('No projects loading method available');
                }
            } catch (error) {
                console.error('Failed to load projects:', error);
                updateStatus('Failed to load projects: ' + error.message, 'error');
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔧 Test page loaded');
            updateStatus('Test page loaded - ready for testing', 'info');
            
            // Check if we have a token
            const token = localStorage.getItem('flori_token');
            if (token) {
                console.log('Found existing token');
                updateStatus('Found existing token - you may be logged in', 'success');
            } else {
                console.log('No token found');
                updateStatus('No token found - please login first', 'info');
            }
        });
    </script>
</body>
</html>
