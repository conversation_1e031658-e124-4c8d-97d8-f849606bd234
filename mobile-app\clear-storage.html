<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Mobile App Storage</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        button {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: #c0392b;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 Clear Mobile App Storage</h1>
        <p>This tool will clear all stored data from the mobile app including tokens, cached data, and settings.</p>
        
        <div class="info">
            <strong>Current Storage Status:</strong>
            <div id="storage-status">Checking...</div>
        </div>
        
        <button onclick="clearAllStorage()">Clear All Storage</button>
        <button onclick="clearTokenOnly()">Clear Token Only</button>
        <button onclick="checkStorage()">Check Storage</button>
        
        <div id="result"></div>
        
        <div style="margin-top: 30px;">
            <a href="index.html" style="color: #e74c3c; text-decoration: none;">← Back to Mobile App</a>
        </div>
    </div>

    <script>
        function showResult(message, type = 'success') {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function checkStorage() {
            const storageInfo = {
                token: localStorage.getItem('flori_token') ? 'Present' : 'Not found',
                user: localStorage.getItem('flori_user') ? 'Present' : 'Not found',
                settings: localStorage.getItem('flori_settings') ? 'Present' : 'Not found'
            };

            const statusDiv = document.getElementById('storage-status');
            statusDiv.innerHTML = `
                <ul style="text-align: left; display: inline-block;">
                    <li>Token: ${storageInfo.token}</li>
                    <li>User Data: ${storageInfo.user}</li>
                    <li>Settings: ${storageInfo.settings}</li>
                </ul>
            `;

            // Show token details if present
            const token = localStorage.getItem('flori_token');
            if (token) {
                const tokenPreview = token.substring(0, 20) + '...';
                showResult(`Token found: ${tokenPreview}`, 'info');
            } else {
                showResult('No token found in storage', 'info');
            }
        }

        function clearTokenOnly() {
            localStorage.removeItem('flori_token');
            localStorage.removeItem('flori_user');
            
            showResult('✅ Token and user data cleared successfully! You will need to login again.');
            checkStorage();
        }

        function clearAllStorage() {
            // Clear all localStorage items
            const keys = Object.keys(localStorage);
            const floriKeys = keys.filter(key => key.startsWith('flori_'));
            
            floriKeys.forEach(key => {
                localStorage.removeItem(key);
            });

            // Also clear any other mobile app related storage
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            localStorage.removeItem('settings');

            // Clear sessionStorage as well
            sessionStorage.clear();

            // Clear any cached data
            if ('caches' in window) {
                caches.keys().then(function(names) {
                    for (let name of names) {
                        caches.delete(name);
                    }
                });
            }

            showResult('✅ All storage cleared successfully! The mobile app has been reset to initial state.');
            checkStorage();
        }

        function resetApp() {
            clearAllStorage();
            
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 2000);
        }

        // Check storage on page load
        document.addEventListener('DOMContentLoaded', () => {
            checkStorage();
        });
    </script>
</body>
</html>
