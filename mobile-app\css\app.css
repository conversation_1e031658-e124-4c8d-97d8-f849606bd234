/* Flori Construction Admin App Styles */

/* Reset and Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f5f5;
    color: #333;
    overflow-x: hidden;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #e74c3c;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Screen Management */
.screen {
    min-height: 100vh;
}

/* Login Screen */
.login-container {
    max-width: 400px;
    margin: 0 auto;
    padding: 40px 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 100vh;
}

.logo {
    text-align: center;
    margin-bottom: 30px;
}

.logo img {
    height: 60px;
    width: auto;
}

.login-form h1 {
    text-align: center;
    margin-bottom: 30px;
    color: #2c3e50;
}

/* Forms */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #e74c3c;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(45deg, #e74c3c, #f39c12);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.btn-ghost {
    background: transparent;
    color: #666;
    border: 1px solid #ddd;
}

.btn-ghost:hover {
    background: #f8f9fa;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Main App Layout */
#main-app {
    display: grid;
    grid-template-areas:
        "header header"
        "sidebar content";
    grid-template-columns: 250px 1fr;
    grid-template-rows: 60px 1fr;
    height: 100vh;
}

/* Header */
.app-header {
    grid-area: header;
    background: white;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #666;
}

#page-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

/* Sidebar */
.sidebar {
    grid-area: sidebar;
    background: #2c3e50;
    color: white;
    overflow-y: auto;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #34495e;
    display: flex;
    align-items: center;
    gap: 10px;
}

.sidebar-header img {
    height: 30px;
    width: auto;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    gap: 12px;
}

.menu-item:hover,
.menu-item.active {
    background: #34495e;
    color: white;
}

.menu-item i {
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    grid-area: content;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
}

/* Pages */
.page {
    display: none;
}

.page.active {
    display: block;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.page-header h2 {
    color: #2c3e50;
    font-size: 24px;
}

/* Dashboard */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #e74c3c, #f39c12);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.stat-info h3 {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-info p {
    color: #666;
    font-size: 14px;
}

/* Filters */
.filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.filters select,
.filters input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
}

/* Grids */
.projects-grid,
.media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.project-card,
.media-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.project-card:hover,
.media-card:hover {
    transform: translateY(-2px);
}

.project-image,
.media-image {
    height: 150px;
    background-size: cover;
    background-position: center;
    position: relative;
}

.project-content,
.media-content {
    padding: 15px;
}

.project-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
}

.project-meta {
    font-size: 12px;
    color: #666;
    margin-bottom: 10px;
}

.project-type {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.project-type.completed {
    background: #d4edda;
    color: #155724;
}

.project-type.ongoing {
    background: #fff3cd;
    color: #856404;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 30px;
}

.pagination button {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pagination button:hover,
.pagination button.active {
    background: #e74c3c;
    color: white;
    border-color: #e74c3c;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-overlay.active {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 12px;
    padding: 20px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
}

.toast {
    background: white;
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #e74c3c;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-left-color: #28a745;
}

.toast.error {
    border-left-color: #dc3545;
}

.toast.warning {
    border-left-color: #ffc107;
}

/* Error Messages */
.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 10px 15px;
    border-radius: 6px;
    margin-top: 15px;
    border: 1px solid #f5c6cb;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-state i {
    color: #ddd;
    margin-bottom: 20px;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #555;
}

.empty-state p {
    margin-bottom: 20px;
}

/* Project Form */
.project-form .form-group {
    margin-bottom: 20px;
}

.project-form textarea {
    min-height: 80px;
    resize: vertical;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

/* Project Cards */
.project-card {
    position: relative;
    overflow: hidden;
}

.project-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-card:hover .project-actions {
    opacity: 1;
}

.project-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
}

.project-date {
    font-size: 12px;
    color: #999;
}

/* Media Components */
.upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.upload-area:hover,
.upload-area.drag-over {
    border-color: #e74c3c;
    background: #fef5f5;
}

.upload-content i {
    color: #ddd;
    margin-bottom: 15px;
}

.upload-content h3 {
    margin-bottom: 10px;
    color: #555;
}

.upload-content p {
    color: #666;
    margin-bottom: 20px;
}

.selected-files {
    margin-bottom: 20px;
}

.selected-files h4 {
    margin-bottom: 15px;
    color: #555;
}

.selected-file {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 8px;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.file-name {
    font-weight: 500;
}

.file-size {
    font-size: 12px;
    color: #666;
}

.upload-options {
    margin-bottom: 20px;
}

/* Media Grid */
.media-card {
    position: relative;
    overflow: hidden;
}

.media-preview {
    height: 150px;
    position: relative;
    overflow: hidden;
}

.media-preview img,
.media-preview video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.media-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.media-card:hover .media-overlay {
    opacity: 1;
}

.file-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #f8f9fa;
    color: #666;
}

.file-extension {
    font-size: 12px;
    font-weight: bold;
    margin-top: 5px;
}

.video-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 24px;
}

.media-info {
    padding: 15px;
}

.media-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #2c3e50;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.media-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.media-date {
    font-size: 12px;
    color: #999;
}

.media-viewer {
    text-align: center;
    max-height: 70vh;
    overflow: auto;
}

.file-preview-large {
    padding: 40px;
    color: #666;
}

.file-preview-large i {
    color: #ddd;
    margin-bottom: 20px;
}

.file-preview-large h3 {
    margin-bottom: 15px;
    color: #555;
}

/* Content Management */
.content-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e74c3c;
}

.section-items {
    display: grid;
    gap: 15px;
}

.content-item {
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 15px;
    background: #fafafa;
}

.content-form {
    margin: 0;
}

.content-form .form-group {
    margin-bottom: 15px;
}

.content-form .form-actions {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #20c997) !important;
    color: white !important;
}

/* Activity List */
.activity-list {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    gap: 15px;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #e74c3c, #f39c12);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    margin-bottom: 5px;
    color: #2c3e50;
    font-size: 14px;
}

.activity-content p {
    color: #666;
    font-size: 12px;
    margin-bottom: 5px;
}

.activity-time {
    font-size: 11px;
    color: #999;
}

/* Settings Sections */
.settings-sections {
    display: grid;
    gap: 20px;
}

.settings-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.settings-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e74c3c;
}

/* Install Banner */
.install-banner {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: white;
    border-radius: 12px;
    padding: 15px 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    border-left: 4px solid #e74c3c;
}

.install-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
}

.install-content span {
    flex: 1;
    font-weight: 500;
    color: #2c3e50;
}

/* Responsive Design */
@media (max-width: 768px) {
    #main-app {
        grid-template-areas:
            "header"
            "content";
        grid-template-columns: 1fr;
        grid-template-rows: 60px 1fr;
    }

    .sidebar {
        position: fixed;
        top: 60px;
        left: -250px;
        width: 250px;
        height: calc(100vh - 60px);
        z-index: 999;
        transition: left 0.3s ease;
    }

    .sidebar.active {
        left: 0;
    }

    .menu-toggle {
        display: block;
    }

    .dashboard-stats {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .projects-grid,
    .media-grid {
        grid-template-columns: 1fr;
    }

    .filters {
        flex-direction: column;
    }

    .page-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
}