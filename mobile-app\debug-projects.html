<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Projects - Flori Construction</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #005a87;
        }
        .log-output {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔧 Projects Debug Tool</h1>
        
        <div class="debug-section">
            <h3>Authentication Status</h3>
            <div id="auth-status" class="status info">Checking authentication...</div>
            <button class="btn" onclick="checkAuth()">Check Auth</button>
            <button class="btn" onclick="testLogin()">Test Login</button>
        </div>

        <div class="debug-section">
            <h3>API Tests</h3>
            <div id="api-status" class="status info">Ready to test API</div>
            <button class="btn" onclick="testPing()">Test Ping</button>
            <button class="btn" onclick="testProjectsAPI()">Test Projects API</button>
            <button class="btn" onclick="testDashboardAPI()">Test Dashboard API</button>
        </div>

        <div class="debug-section">
            <h3>Projects Manager</h3>
            <div id="manager-status" class="status info">Checking manager status...</div>
            <button class="btn" onclick="checkManager()">Check Manager</button>
            <button class="btn" onclick="testProjectsLoad()">Test Load Projects</button>
            <button class="btn" onclick="initializeManager()">Initialize Manager</button>
        </div>

        <div class="debug-section">
            <h3>Console Log</h3>
            <div id="log-output" class="log-output"></div>
            <button class="btn" onclick="clearLog()">Clear Log</button>
        </div>
    </div>

    <script>
        // Override console.log to capture output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToLog(message, type = 'log') {
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog(args.join(' '), 'warn');
        };

        function clearLog() {
            document.getElementById('log-output').innerHTML = '';
        }

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        async function checkAuth() {
            console.log('🔍 Checking authentication...');
            
            const token = localStorage.getItem('flori_token');
            if (!token) {
                updateStatus('auth-status', 'No token found in localStorage', 'error');
                return;
            }
            
            try {
                const response = await fetch('../api/auth.php?action=verify', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                console.log('Auth response:', data);
                
                if (data.success) {
                    updateStatus('auth-status', `Authenticated as: ${data.user.username}`, 'success');
                } else {
                    updateStatus('auth-status', 'Authentication failed: ' + (data.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                console.error('Auth check failed:', error);
                updateStatus('auth-status', 'Auth check failed: ' + error.message, 'error');
            }
        }

        async function testLogin() {
            console.log('🔑 Testing login...');
            
            const username = prompt('Enter username:');
            const password = prompt('Enter password:');
            
            if (!username || !password) {
                updateStatus('auth-status', 'Login cancelled', 'warning');
                return;
            }
            
            try {
                const response = await fetch('../api/auth.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                console.log('Login response:', data);
                
                if (data.success) {
                    localStorage.setItem('flori_token', data.token);
                    updateStatus('auth-status', 'Login successful!', 'success');
                } else {
                    updateStatus('auth-status', 'Login failed: ' + (data.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                console.error('Login failed:', error);
                updateStatus('auth-status', 'Login failed: ' + error.message, 'error');
            }
        }

        async function testPing() {
            console.log('🏓 Testing ping...');
            
            try {
                const response = await fetch('../api/mobile.php?action=ping');
                const data = await response.json();
                console.log('Ping response:', data);
                
                if (data.success) {
                    updateStatus('api-status', 'API is accessible', 'success');
                } else {
                    updateStatus('api-status', 'Ping failed: ' + (data.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                console.error('Ping failed:', error);
                updateStatus('api-status', 'Ping failed: ' + error.message, 'error');
            }
        }

        async function testProjectsAPI() {
            console.log('📋 Testing projects API...');
            
            const token = localStorage.getItem('flori_token');
            if (!token) {
                updateStatus('api-status', 'No token available for API test', 'error');
                return;
            }
            
            try {
                const response = await fetch('../api/mobile.php?action=projects&page=1&limit=5', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                console.log('Projects API response:', data);
                
                if (data.success) {
                    const projectCount = data.data?.projects?.length || 0;
                    updateStatus('api-status', `Projects API working! Found ${projectCount} projects`, 'success');
                } else {
                    updateStatus('api-status', 'Projects API failed: ' + (data.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                console.error('Projects API failed:', error);
                updateStatus('api-status', 'Projects API failed: ' + error.message, 'error');
            }
        }

        async function testDashboardAPI() {
            console.log('📊 Testing dashboard API...');
            
            const token = localStorage.getItem('flori_token');
            if (!token) {
                updateStatus('api-status', 'No token available for dashboard test', 'error');
                return;
            }
            
            try {
                const response = await fetch('../api/mobile.php?action=dashboard', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                console.log('Dashboard API response:', data);
                
                if (data.success) {
                    updateStatus('api-status', 'Dashboard API working!', 'success');
                } else {
                    updateStatus('api-status', 'Dashboard API failed: ' + (data.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                console.error('Dashboard API failed:', error);
                updateStatus('api-status', 'Dashboard API failed: ' + error.message, 'error');
            }
        }

        function checkManager() {
            console.log('🔍 Checking ProjectsManager...');
            
            if (window.ProjectsManager) {
                const status = `Manager available - Initialized: ${window.ProjectsManager.isInitialized}`;
                updateStatus('manager-status', status, 'success');
                console.log('ProjectsManager state:', {
                    isInitialized: window.ProjectsManager.isInitialized,
                    currentPage: window.ProjectsManager.currentPage,
                    itemsPerPage: window.ProjectsManager.itemsPerPage
                });
            } else {
                updateStatus('manager-status', 'ProjectsManager not available', 'error');
            }
        }

        async function testProjectsLoad() {
            console.log('🚀 Testing projects load...');
            
            if (!window.ProjectsManager) {
                updateStatus('manager-status', 'ProjectsManager not available', 'error');
                return;
            }
            
            try {
                await window.ProjectsManager.testLoad();
                updateStatus('manager-status', 'Projects load test completed', 'success');
            } catch (error) {
                console.error('Projects load test failed:', error);
                updateStatus('manager-status', 'Projects load test failed: ' + error.message, 'error');
            }
        }

        function initializeManager() {
            console.log('🔧 Initializing ProjectsManager...');
            
            try {
                // Load the projects.js script if not already loaded
                if (!window.ProjectsManager) {
                    const script = document.createElement('script');
                    script.src = 'js/projects.js';
                    script.onload = () => {
                        console.log('Projects.js loaded');
                        updateStatus('manager-status', 'ProjectsManager script loaded', 'success');
                    };
                    script.onerror = () => {
                        console.error('Failed to load projects.js');
                        updateStatus('manager-status', 'Failed to load projects.js', 'error');
                    };
                    document.head.appendChild(script);
                } else {
                    updateStatus('manager-status', 'ProjectsManager already available', 'success');
                }
            } catch (error) {
                console.error('Failed to initialize manager:', error);
                updateStatus('manager-status', 'Failed to initialize: ' + error.message, 'error');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔧 Debug tool loaded');
            checkAuth();
            checkManager();
        });
    </script>
</body>
</html>
