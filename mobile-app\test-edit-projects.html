<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Edit Projects - Flori Construction</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/app.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list .icon {
            color: #28a745;
            width: 20px;
        }
        .demo-projects {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .status-indicator {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: 500;
        }
        .status-success { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Edit Projects Feature Test</h1>
        
        <div class="status-indicator status-info" id="status">
            Ready to test edit project features
        </div>

        <div class="test-section">
            <h3>✨ New Features Implemented</h3>
            <ul class="feature-list">
                <li>
                    <i class="fas fa-eye icon"></i>
                    <span><strong>View Project Details:</strong> Click the eye icon to view comprehensive project information</span>
                </li>
                <li>
                    <i class="fas fa-edit icon"></i>
                    <span><strong>Edit Project:</strong> Click the edit icon to modify project details with pre-filled form</span>
                </li>
                <li>
                    <i class="fas fa-save icon"></i>
                    <span><strong>Update Project:</strong> Save changes with validation and error handling</span>
                </li>
                <li>
                    <i class="fas fa-star icon"></i>
                    <span><strong>Featured Projects:</strong> Mark projects as featured with visual indicators</span>
                </li>
                <li>
                    <i class="fas fa-calendar icon"></i>
                    <span><strong>Date Management:</strong> Set start and end dates with proper formatting</span>
                </li>
                <li>
                    <i class="fas fa-dollar-sign icon"></i>
                    <span><strong>Project Value:</strong> Track project financial value</span>
                </li>
                <li>
                    <i class="fas fa-mobile-alt icon"></i>
                    <span><strong>Mobile Responsive:</strong> Optimized for mobile devices</span>
                </li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 Test Actions</h3>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn btn-primary" onclick="testLogin()">
                    <i class="fas fa-sign-in-alt"></i> Login
                </button>
                <button class="btn btn-primary" onclick="loadProjects()">
                    <i class="fas fa-download"></i> Load Projects
                </button>
                <button class="btn btn-primary" onclick="createSampleProject()">
                    <i class="fas fa-plus"></i> Create Sample Project
                </button>
                <button class="btn btn-secondary" onclick="clearStatus()">
                    <i class="fas fa-eraser"></i> Clear Status
                </button>
            </div>
        </div>

        <!-- Simulate the projects page structure -->
        <div id="projects-page" class="page active">
            <div class="page-header">
                <h2>Projects</h2>
                <button id="add-project-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add Project
                </button>
            </div>

            <div class="filters">
                <select id="project-type-filter">
                    <option value="">All Projects</option>
                    <option value="completed">Completed</option>
                    <option value="ongoing">Ongoing</option>
                </select>
                <input type="search" id="project-search" placeholder="Search projects...">
            </div>

            <div id="projects-list" class="projects-grid demo-projects">
                <!-- Sample projects for testing -->
                <div class="project-card" data-id="1">
                    <div class="project-image" style="background: linear-gradient(45deg, #e74c3c, #f39c12);">
                        <div class="project-actions">
                            <button class="btn btn-ghost btn-sm" onclick="testViewProject(1)" title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-ghost btn-sm" onclick="testEditProject(1)" title="Edit Project">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-ghost btn-sm" onclick="testDeleteProject(1)" title="Delete Project">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="project-content">
                        <h3 class="project-title">Sample Residential Project</h3>
                        <p class="project-meta">
                            <i class="fas fa-map-marker-alt"></i> Toronto, ON
                        </p>
                        <p class="project-meta">
                            <i class="fas fa-user"></i> John Smith
                        </p>
                        <div class="project-footer">
                            <span class="project-type completed">Completed</span>
                            <span class="featured-badge"><i class="fas fa-star"></i> Featured</span>
                        </div>
                    </div>
                </div>

                <div class="project-card" data-id="2">
                    <div class="project-image" style="background: linear-gradient(45deg, #3498db, #2ecc71);">
                        <div class="project-actions">
                            <button class="btn btn-ghost btn-sm" onclick="testViewProject(2)" title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-ghost btn-sm" onclick="testEditProject(2)" title="Edit Project">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-ghost btn-sm" onclick="testDeleteProject(2)" title="Delete Project">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="project-content">
                        <h3 class="project-title">Commercial Building Renovation</h3>
                        <p class="project-meta">
                            <i class="fas fa-map-marker-alt"></i> Vancouver, BC
                        </p>
                        <p class="project-meta">
                            <i class="fas fa-user"></i> ABC Corporation
                        </p>
                        <div class="project-footer">
                            <span class="project-type ongoing">Ongoing</span>
                        </div>
                    </div>
                </div>
            </div>

            <div id="projects-pagination" class="pagination">
                <!-- Pagination will be loaded here -->
            </div>
        </div>

        <div class="test-section">
            <h3>📋 Test Instructions</h3>
            <ol>
                <li><strong>Login:</strong> Click the "Login" button and enter your credentials</li>
                <li><strong>View Project:</strong> Hover over a project card and click the eye icon to view details</li>
                <li><strong>Edit Project:</strong> Click the edit icon to open the edit form with pre-filled data</li>
                <li><strong>Test Form:</strong> Modify fields and save to test the update functionality</li>
                <li><strong>Mobile Test:</strong> Resize your browser to test mobile responsiveness</li>
            </ol>
        </div>
    </div>

    <!-- Modal overlay for testing -->
    <div class="modal-overlay" id="modal-overlay">
        <div class="modal-content" id="modal-content">
            <!-- Modal content will be inserted here -->
        </div>
    </div>

    <!-- Toast container -->
    <div class="toast-container" id="toast-container"></div>

    <!-- Load the required scripts -->
    <script src="js/app.js"></script>
    <script src="js/projects.js"></script>

    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status-indicator status-${type}`;
        }

        function clearStatus() {
            updateStatus('Ready to test edit project features', 'info');
        }

        async function testLogin() {
            updateStatus('Testing login...', 'info');
            
            const username = prompt('Enter username:') || 'admin';
            const password = prompt('Enter password:') || 'admin123';
            
            try {
                const response = await fetch('../api/auth.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    localStorage.setItem('flori_token', data.token);
                    if (window.floriAdmin) {
                        window.floriAdmin.token = data.token;
                        window.floriAdmin.user = data.user;
                    }
                    updateStatus('Login successful! You can now test edit features.', 'success');
                } else {
                    updateStatus('Login failed: ' + (data.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                updateStatus('Login error: ' + error.message, 'error');
            }
        }

        async function loadProjects() {
            updateStatus('Loading projects...', 'info');
            
            try {
                if (window.ProjectsManager) {
                    await window.ProjectsManager.load();
                    updateStatus('Projects loaded successfully!', 'success');
                } else {
                    throw new Error('ProjectsManager not available');
                }
            } catch (error) {
                updateStatus('Failed to load projects: ' + error.message, 'error');
            }
        }

        function testViewProject(id) {
            updateStatus(`Testing view project ${id}...`, 'info');
            
            // Sample project data for testing
            const sampleProject = {
                id: id,
                title: id === 1 ? 'Sample Residential Project' : 'Commercial Building Renovation',
                project_type: id === 1 ? 'completed' : 'ongoing',
                location: id === 1 ? 'Toronto, ON' : 'Vancouver, BC',
                client_name: id === 1 ? 'John Smith' : 'ABC Corporation',
                short_description: 'This is a sample project for testing the view functionality.',
                description: 'A comprehensive description of the project with multiple lines of text to demonstrate how the view modal handles longer content.',
                start_date: '2023-01-15',
                end_date: id === 1 ? '2023-06-30' : null,
                project_value: id === 1 ? 150000 : 250000,
                is_featured: id === 1 ? true : false
            };
            
            if (window.ProjectsManager) {
                window.ProjectsManager.showViewProjectModal(sampleProject);
                updateStatus('View project modal opened', 'success');
            } else {
                updateStatus('ProjectsManager not available', 'error');
            }
        }

        function testEditProject(id) {
            updateStatus(`Testing edit project ${id}...`, 'info');
            
            // Sample project data for testing
            const sampleProject = {
                id: id,
                title: id === 1 ? 'Sample Residential Project' : 'Commercial Building Renovation',
                project_type: id === 1 ? 'completed' : 'ongoing',
                location: id === 1 ? 'Toronto, ON' : 'Vancouver, BC',
                client_name: id === 1 ? 'John Smith' : 'ABC Corporation',
                short_description: 'This is a sample project for testing the edit functionality.',
                description: 'A comprehensive description of the project.',
                start_date: '2023-01-15',
                end_date: id === 1 ? '2023-06-30' : '',
                project_value: id === 1 ? 150000 : 250000,
                is_featured: id === 1 ? true : false
            };
            
            if (window.ProjectsManager) {
                window.ProjectsManager.showEditProjectModal(sampleProject);
                updateStatus('Edit project modal opened', 'success');
            } else {
                updateStatus('ProjectsManager not available', 'error');
            }
        }

        function testDeleteProject(id) {
            updateStatus(`Testing delete project ${id}...`, 'info');
            
            if (confirm('This is a test. Are you sure you want to delete this project?')) {
                updateStatus('Delete confirmed (test mode)', 'success');
            } else {
                updateStatus('Delete cancelled', 'info');
            }
        }

        function createSampleProject() {
            updateStatus('Opening create project modal...', 'info');
            
            if (window.ProjectsManager) {
                window.ProjectsManager.showAddProjectModal();
                updateStatus('Create project modal opened', 'success');
            } else {
                updateStatus('ProjectsManager not available', 'error');
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            updateStatus('Test page loaded - ready for testing', 'success');
            
            // Check if we have a token
            const token = localStorage.getItem('flori_token');
            if (token) {
                updateStatus('Found existing token - you may be logged in', 'success');
            }
        });
    </script>
</body>
</html>
